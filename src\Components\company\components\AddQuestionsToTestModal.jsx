import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  DocumentTextIcon,
  ClockIcon,
  UserGroupIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import useCompanyStore from '../../../store/companyStore';
import QuestionFilterAndBundle from './QuestionFilterAndBundle';
import toast from 'react-hot-toast';

const AddQuestionsToTestModal = ({ 
  isOpen, 
  onClose, 
  test,
  onQuestionsAdded 
}) => {
  const { addQuestionsToTest, loading } = useCompanyStore();
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [questionsToAdd, setQuestionsToAdd] = useState([]);
  const [isAdding, setIsAdding] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setQuestionsToAdd([]);
    }
  }, [isOpen]);

  const getTestStatus = (test) => {
    const now = new Date();
    const startDate = new Date(test.scheduledDate);
    const endDate = new Date(test.endDate);
    const hasParticipants = test.participants && test.participants.length > 0;
    const hasStartedParticipants = test.participants?.some(p => p.status === 'in-progress' || p.status === 'completed');

    if (!test.isActive) return { 
      status: 'inactive', 
      canModify: true, 
      warning: null 
    };
    
    if (hasStartedParticipants) return { 
      status: 'started', 
      canModify: false, 
      warning: 'Cannot modify test - some candidates have already started or completed the test' 
    };
    
    if (now >= startDate && now <= endDate && hasParticipants) return { 
      status: 'active-assigned', 
      canModify: false, 
      warning: 'Cannot modify test - test is active and candidates are assigned' 
    };
    
    if (now >= startDate) return { 
      status: 'active', 
      canModify: false, 
      warning: 'Cannot modify test - test has already started' 
    };
    
    return { 
      status: 'scheduled', 
      canModify: true, 
      warning: hasParticipants ? 'Test has assigned candidates. Adding questions will affect their test experience.' : null 
    };
  };

  const testStatus = test ? getTestStatus(test) : { canModify: false, warning: null };

  const handleAddQuestions = (questions) => {
    setQuestionsToAdd(prev => {
      const newQuestions = questions.filter(q => 
        !prev.some(existing => existing.questionId === q.questionId) &&
        !test.questions.some(existing => existing.questionId === q.questionId)
      );
      return [...prev, ...newQuestions];
    });
    setShowQuestionModal(false);
  };

  const removeQuestionFromList = (questionId) => {
    setQuestionsToAdd(prev => prev.filter(q => q.questionId !== questionId));
  };

  const updateQuestionPoints = (questionId, points) => {
    setQuestionsToAdd(prev =>
      prev.map(q => q.questionId === questionId ? { ...q, points } : q)
    );
  };

  const handleConfirmAdd = async () => {
    if (questionsToAdd.length === 0) {
      toast.error('No questions to add');
      return;
    }

    if (!testStatus.canModify) {
      toast.error('Cannot modify this test');
      return;
    }

    setIsAdding(true);
    try {
      const result = await addQuestionsToTest(test._id, questionsToAdd);
      if (result) {
        toast.success(`Added ${questionsToAdd.length} questions to test`);
        onQuestionsAdded && onQuestionsAdded(questionsToAdd);
        onClose();
      } else {
        toast.error('Failed to add questions to test');
      }
    } catch (error) {
      toast.error('An error occurred while adding questions');
    } finally {
      setIsAdding(false);
    }
  };

  if (!isOpen || !test) return null;

  return (
    <AnimatePresence key="add-questions-modal">
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">Add Questions to Test</h2>
                <p className="text-green-100">{test.testName}</p>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[70vh]">
            {/* Warning Section */}
            {!testStatus.canModify && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-semibold text-red-800 mb-1">Cannot Modify Test</h3>
                    <p className="text-red-700 text-sm">{testStatus.warning}</p>
                  </div>
                </div>
              </div>
            )}

            {testStatus.warning && testStatus.canModify && (
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-semibold text-yellow-800 mb-1">Warning</h3>
                    <p className="text-yellow-700 text-sm">{testStatus.warning}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Test Information */}
            <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <DocumentTextIcon className="h-5 w-5 text-gray-600" />
                  <span className="font-medium text-gray-800">Current Questions</span>
                </div>
                <span className="text-2xl font-bold text-blue-600">{test.questions?.length || 0}</span>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <ClockIcon className="h-5 w-5 text-gray-600" />
                  <span className="font-medium text-gray-800">Duration</span>
                </div>
                <span className="text-2xl font-bold text-green-600">{test.duration} min</span>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <UserGroupIcon className="h-5 w-5 text-gray-600" />
                  <span className="font-medium text-gray-800">Participants</span>
                </div>
                <span className="text-2xl font-bold text-purple-600">{test.participants?.length || 0}</span>
              </div>
            </div>

            {/* Add Questions Section */}
            {testStatus.canModify && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-800">Add New Questions</h3>
                  <button
                    onClick={() => setShowQuestionModal(true)}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                  >
                    <PlusIcon className="h-5 w-5" />
                    Browse Questions
                  </button>
                </div>

                {/* Questions to Add */}
                {questionsToAdd.length > 0 && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-4">
                        <span className="text-sm font-medium text-blue-800">
                          {questionsToAdd.length} question(s) ready to add
                        </span>
                        <span className="text-sm text-blue-600">
                          Total points: {questionsToAdd.reduce((sum, q) => sum + (q.points || 1), 0)}
                        </span>
                      </div>
                    </div>

                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {questionsToAdd.map((question, index) => (
                        <motion.div
                          key={`add-question-${question.questionId || index}-${index}`}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="p-4 border border-gray-200 rounded-lg bg-white"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="font-medium text-gray-800 mb-2">
                                Question {(test.questions?.length || 0) + index + 1}
                              </div>
                              <div className="text-sm text-gray-600 mb-3">
                                ID: {question.questionId}
                              </div>
                              <div className="flex items-center gap-4">
                                <label className="text-sm font-medium text-gray-700">Points:</label>
                                <input
                                  type="number"
                                  min="1"
                                  max="10"
                                  value={question.points || 1}
                                  onChange={(e) => updateQuestionPoints(question.questionId, parseInt(e.target.value) || 1)}
                                  className="w-16 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                              </div>
                            </div>
                            <button
                              onClick={() => removeQuestionFromList(question.questionId)}
                              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                            >
                              <XMarkIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}

                {questionsToAdd.length === 0 && (
                  <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                    <DocumentTextIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No Questions Selected</h3>
                    <p className="text-gray-500 mb-4">Browse and select questions to add to this test</p>
                    <button
                      onClick={() => setShowQuestionModal(true)}
                      className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                    >
                      Browse Questions
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Read-only view for non-modifiable tests */}
            {!testStatus.canModify && (
              <div className="text-center py-12">
                <ExclamationTriangleIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">Test Cannot Be Modified</h3>
                <p className="text-gray-500">This test is in a state that prevents question modifications</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-600">
              {questionsToAdd.length > 0 && (
                <span>Ready to add {questionsToAdd.length} question(s)</span>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              
              {testStatus.canModify && questionsToAdd.length > 0 && (
                <button
                  onClick={handleConfirmAdd}
                  disabled={isAdding}
                  className="flex items-center gap-2 px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium"
                >
                  {isAdding ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      Adding...
                    </>
                  ) : (
                    <>
                      <CheckCircleIcon className="h-4 w-4" />
                      Add {questionsToAdd.length} Question(s)
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Question Selection Modal */}
      <QuestionFilterAndBundle
        isOpen={showQuestionModal}
        onClose={() => setShowQuestionModal(false)}
        onAddToTest={handleAddQuestions}
        existingQuestions={[...test.questions, ...questionsToAdd]}
      />
    </AnimatePresence>
  );
};

export default AddQuestionsToTestModal;
