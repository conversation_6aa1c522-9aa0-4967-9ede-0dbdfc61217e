import { create } from 'zustand';
import axios from 'axios';
import {
    COMPANY_ENDPOINTS,
    QUESTION_ENDPOINTS,
    TEST_ENDPOINTS,
    CANDIDATE_ENDPOINTS,
    QUESTION_BUNDLE_ENDPOINTS
} from '../lib/constants';
import {
    mockCandidates,
    mockQuestionBundles
} from '../Components/company/data/mockQuestionData';

const axiosInstance = axios.create({
    withCredentials: true,
});

// Add request interceptor to include auth token
axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

const useCompanyStore = create((set) => ({
    loading: false,
    error: null,
    company: null,
    dashboard: null,
    jobs: [],
    jobDetails: null,
    questions: [],
    tests: [],
    testDetails: null,

    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),

    // Company Profile
    createCompanyProfile: async (data) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(COMPANY_ENDPOINTS.PROFILE, data);
            set({ company: res.data.company });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Profile creation failed' });
        } finally {
            set({ loading: false });
        }
    },

    getCompanyProfile: async () => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.PROFILE);
            set({ company: res.data.company });
            return res.data.company;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch profile failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },
    updateCompanyProfile: async (data) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.put(COMPANY_ENDPOINTS.PROFILE, data);
            set({ company: res.data.company });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update failed' });
        } finally {
            set({ loading: false });
        }
    },

    // Jobs
    createJob: async (jobData) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(COMPANY_ENDPOINTS.JOBS, jobData);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job creation failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },

    getJobs: async () => {
        set({ loading: true });
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.JOBS);
            set({ jobs: res.data.jobs || [] });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch jobs failed' });
        } finally {
            set({ loading: false });
        }
    },

    getJobById: async (jobId) => {
        set({ loading: true });
        try {
            const res = await axiosInstance.get(`${COMPANY_ENDPOINTS.JOBS}/${jobId}`);
            set({ jobDetails: res.data.job });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job fetch failed' });
        } finally {
            set({ loading: false });
        }
    },

    updateJob: async (jobId, data) => {
        set({ loading: true });
        try {
            const res = await axiosInstance.put(`${COMPANY_ENDPOINTS.JOBS}/${jobId}`, data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job update failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },
    updateJobStatus: async (jobId, isActive) => {
        try {
            const res = await axiosInstance.patch(COMPANY_ENDPOINTS.JOB_STATUS(jobId), { isActive });

            // Update the job in the local state
            set((state) => ({
                jobs: state.jobs.map(job =>
                    job._id === jobId ? { ...job, isActive } : job
                )
            }));

            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Status update failed' });
            return null;
        }
    },
    getJobApplications: async (jobId) => {
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.JOB_APPLICATIONS(jobId));
            return res.data.applications;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch applications failed' });
            return [];
        }
    },
    // Dashboard
    getDashboardData: async () => {
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.DASHBOARD);
            set({ dashboard: res.data });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Dashboard fetch failed' });
        }
    },
    // Questions
    createQuestion: async (data) => {
        try {
            const res = await axiosInstance.post(QUESTION_ENDPOINTS.CREATE, data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Create question failed' });
            return null;
        }
    },
    getQuestions: async () => {
        try {
            const res = await axiosInstance.get(QUESTION_ENDPOINTS.GET_ALL);
            const questions = res.data.questions || [];

            // Transform the API response to match our expected format
            const transformedQuestions = questions.map(q => ({
                _id: q._id,
                questionText: q.questionText,
                type: q.questionType, // Map questionType to type
                category: q.category,
                difficulty: q.difficulty,
                options: q.options || [],
                correctAnswer: q.correctAnswer,
                explanation: q.explanation,
                points: q.points || 1,
                tags: [q.category.toLowerCase(), q.difficulty.toLowerCase()], // Generate tags
                isActive: q.isActive,
                createdBy: q.createdBy,
                createdAt: q.createdAt,
                updatedAt: q.updatedAt
            }));

            set({ questions: transformedQuestions });
            return transformedQuestions;
        } catch (err) {
            console.error('Error fetching questions:', err);
            set({ error: err?.response?.data?.error || 'Fetch questions failed' });
            return [];
        }
    },
    updateQuestion: async (id, data) => {
        try {
            const res = await axiosInstance.patch(QUESTION_ENDPOINTS.BY_ID(id), data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update question failed' });
            return null;
        }
    },
    deleteQuestion: async (id) => {
        try {
            await axiosInstance.delete(QUESTION_ENDPOINTS.BY_ID(id));
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Delete question failed' });
        }
    },
    // Tests
    createTest: async (data) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.CREATE, data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Create test failed' });
            return null;
        }
    },
    getTests: async () => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.GET_ALL);
            set({ tests: res.data.tests });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch tests failed' });
        }
    },
    getTestDetails: async (id) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.BY_ID(id));
            set({ testDetails: res.data.test });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Test detail fetch failed' });
        }
    },
    assignTest: async (testId, assignData) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.ASSIGN(testId), assignData);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Assign test failed' });
            return null;
        }
    },
    getTestResults: async (testId) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.RESULTS(testId));
            return res.data.results;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch results failed' });
            return [];
        }
    },

    getTestAnalytics: async (testId) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.ANALYTICS(testId));
            return res.data.analytics;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Analytics fetch failed' });
            return null;
        }
    },

    submitFeedback: async (testId, participantId, feedback) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.PARTICIPANT_FEEDBACK(testId, participantId), feedback);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Feedback submit failed' });
            return null;
        }
    },

    updateTest: async (testId, data) => {
        try {
            const res = await axiosInstance.put(TEST_ENDPOINTS.UPDATE(testId), data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update test failed' });
            return null;
        }
    },

    deleteTest: async (testId) => {
        try {
            await axiosInstance.delete(TEST_ENDPOINTS.DELETE(testId));
            // Remove from local state
            set((state) => ({
                tests: state.tests.filter(test => test._id !== testId)
            }));
            return { success: true };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Delete test failed' });
            return null;
        }
    },

    addQuestionsToTest: async (testId, questions) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.ADD_QUESTIONS(testId), { questions });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Add questions failed' });
            return null;
        }
    },

    removeQuestionsFromTest: async (testId, questionIds) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.REMOVE_QUESTIONS(testId), { questionIds });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Remove questions failed' });
            return null;
        }
    },

    assignCandidatesToTest: async (testId, candidateIds) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.ASSIGN_CANDIDATES(testId), { candidateIds });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Assign candidates failed' });
            return null;
        }
    },

    // Candidates
    getCandidates: async () => {
        try {
            // For demo purposes, use mock data
            set({ candidates: mockCandidates });
            return mockCandidates;

            // const res = await axiosInstance.get(CANDIDATE_ENDPOINTS.GET_ALL);
            // set({ candidates: res.data.candidates || [] });
            // return res.data.candidates;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch candidates failed' });
            return [];
        }
    },

    searchCandidates: async (searchTerm, filters = {}) => {
        try {
            // For demo purposes, filter mock data
            let filtered = mockCandidates;

            if (searchTerm) {
                filtered = filtered.filter(candidate =>
                    candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    candidate.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    candidate.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
                );
            }

            if (filters.experience) {
                filtered = filtered.filter(candidate => {
                    const exp = parseInt(candidate.experience);
                    switch (filters.experience) {
                        case '0-1': return exp <= 1;
                        case '1-3': return exp >= 1 && exp <= 3;
                        case '3-5': return exp >= 3 && exp <= 5;
                        case '5+': return exp >= 5;
                        default: return true;
                    }
                });
            }

            if (filters.skills) {
                filtered = filtered.filter(candidate =>
                    candidate.skills.some(skill =>
                        skill.toLowerCase().includes(filters.skills.toLowerCase())
                    )
                );
            }

            if (filters.location) {
                filtered = filtered.filter(candidate =>
                    candidate.location.toLowerCase().includes(filters.location.toLowerCase())
                );
            }

            if (filters.status) {
                filtered = filtered.filter(candidate => candidate.status === filters.status);
            }

            return filtered;

            // const params = new URLSearchParams({ search: searchTerm, ...filters });
            // const res = await axiosInstance.get(`${CANDIDATE_ENDPOINTS.SEARCH}?${params}`);
            // return res.data.candidates || [];
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Search candidates failed' });
            return [];
        }
    },

    getAvailableCandidatesForTest: async (testId) => {
        try {
            // For demo purposes, return all available candidates
            return mockCandidates.filter(candidate => candidate.status === 'available');

            // const res = await axiosInstance.get(`${CANDIDATE_ENDPOINTS.AVAILABLE_FOR_TEST}?testId=${testId}`);
            // return res.data.candidates || [];
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch available candidates failed' });
            return [];
        }
    },

    // Question Categories and Filtering
    getQuestionCategories: async () => {
        try {
            // Get categories from existing questions
            const state = get();
            if (state.questions && state.questions.length > 0) {
                const categories = [...new Set(state.questions.map(q => q.category).filter(Boolean))];
                set({ questionCategories: categories });
                return categories;
            }

            // If no questions loaded yet, fetch them first
            await get().getQuestions();
            const updatedState = get();
            const categories = [...new Set(updatedState.questions.map(q => q.category).filter(Boolean))];
            set({ questionCategories: categories });
            return categories;
        } catch (err) {
            console.error('Error fetching categories:', err);
            set({ error: err?.response?.data?.error || 'Fetch categories failed' });
            return [];
        }
    },

    getQuestionsByCategory: async (category) => {
        try {
            const state = get();

            // If questions not loaded, fetch them first
            if (!state.questions || state.questions.length === 0) {
                await get().getQuestions();
            }

            // Filter questions by category
            const updatedState = get();
            const filtered = updatedState.questions.filter(q => q.category === category);
            return filtered;
        } catch (err) {
            console.error('Error fetching questions by category:', err);
            set({ error: err?.response?.data?.error || 'Fetch questions by category failed' });
            return [];
        }
    },

    filterQuestions: async (filters) => {
        try {
            const state = get();

            // If questions not loaded, fetch them first
            if (!state.questions || state.questions.length === 0) {
                await get().getQuestions();
            }

            const updatedState = get();
            let filtered = updatedState.questions;

            if (filters.searchTerm) {
                filtered = filtered.filter(q =>
                    q.questionText.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
                    q.category?.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
                    q.tags?.some(tag => tag.toLowerCase().includes(filters.searchTerm.toLowerCase()))
                );
            }

            if (filters.category) {
                filtered = filtered.filter(q => q.category === filters.category);
            }

            if (filters.difficulty) {
                filtered = filtered.filter(q => q.difficulty === filters.difficulty);
            }

            if (filters.type) {
                filtered = filtered.filter(q => q.type === filters.type);
            }

            return filtered;
        } catch (err) {
            console.error('Error filtering questions:', err);
            set({ error: err?.response?.data?.error || 'Filter questions failed' });
            return [];
        }
    },

    // Question Bundles
    createQuestionBundle: async (bundleData) => {
        try {
            // For demo purposes, simulate creation
            const newBundle = {
                _id: 'b' + Date.now(),
                ...bundleData,
                createdAt: new Date()
            };

            set((state) => ({
                questionBundles: [...state.questionBundles, newBundle]
            }));

            return { success: true, bundle: newBundle };

            // const res = await axiosInstance.post(QUESTION_BUNDLE_ENDPOINTS.CREATE, bundleData);
            // return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Create bundle failed' });
            return null;
        }
    },

    getQuestionBundles: async () => {
        try {
            // For demo purposes, use mock data
            set({ questionBundles: mockQuestionBundles });
            return mockQuestionBundles;

            // const res = await axiosInstance.get(QUESTION_BUNDLE_ENDPOINTS.GET_ALL);
            // set({ questionBundles: res.data.bundles || [] });
            // return res.data.bundles;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch bundles failed' });
            return [];
        }
    },

    updateQuestionBundle: async (bundleId, bundleData) => {
        try {
            // For demo purposes, simulate update
            set((state) => ({
                questionBundles: state.questionBundles.map(bundle =>
                    bundle._id === bundleId ? { ...bundle, ...bundleData } : bundle
                )
            }));

            return { success: true };

            // const res = await axiosInstance.put(QUESTION_BUNDLE_ENDPOINTS.UPDATE(bundleId), bundleData);
            // return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update bundle failed' });
            return null;
        }
    },

    deleteQuestionBundle: async (bundleId) => {
        try {
            // For demo purposes, simulate deletion
            set((state) => ({
                questionBundles: state.questionBundles.filter(bundle => bundle._id !== bundleId)
            }));

            return { success: true };

            // await axiosInstance.delete(QUESTION_BUNDLE_ENDPOINTS.DELETE(bundleId));
            // return { success: true };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Delete bundle failed' });
            return null;
        }
    },

    clearError: () => set({ error: null }),
}));

export default useCompanyStore;
