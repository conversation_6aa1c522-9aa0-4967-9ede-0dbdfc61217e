import { create } from 'zustand';
import axios from 'axios';
import {
    COMPANY_ENDPOINTS,
    QUESTION_ENDPOINTS,
    TEST_ENDPOINTS,
    CANDIDATE_ENDPOINTS,
    QUESTION_BUNDLE_ENDPOINTS
} from '../lib/constants';

const axiosInstance = axios.create({
    withCredentials: true,
});

// Add request interceptor to include auth token
axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem('token');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

const useCompanyStore = create((set) => ({
    loading: false,
    error: null,
    company: null,
    dashboard: null,
    jobs: [],
    jobDetails: null,
    questions: [],
    questionCategories: [],
    questionBundles: [],
    candidates: [],
    tests: [],
    testDetails: null,

    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),

    // Company Profile
    createCompanyProfile: async (data) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(COMPANY_ENDPOINTS.PROFILE, data);
            set({ company: res.data.company });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Profile creation failed' });
        } finally {
            set({ loading: false });
        }
    },

    getCompanyProfile: async () => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.PROFILE);
            set({ company: res.data.company });
            return res.data.company;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch profile failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },
    updateCompanyProfile: async (data) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.put(COMPANY_ENDPOINTS.PROFILE, data);
            set({ company: res.data.company });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update failed' });
        } finally {
            set({ loading: false });
        }
    },

    // Jobs
    createJob: async (jobData) => {
        set({ loading: true, error: null });
        try {
            const res = await axiosInstance.post(COMPANY_ENDPOINTS.JOBS, jobData);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job creation failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },

    getJobs: async () => {
        set({ loading: true });
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.JOBS);
            set({ jobs: res.data.jobs || [] });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch jobs failed' });
        } finally {
            set({ loading: false });
        }
    },

    getJobById: async (jobId) => {
        set({ loading: true });
        try {
            const res = await axiosInstance.get(`${COMPANY_ENDPOINTS.JOBS}/${jobId}`);
            set({ jobDetails: res.data.job });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job fetch failed' });
        } finally {
            set({ loading: false });
        }
    },

    updateJob: async (jobId, data) => {
        set({ loading: true });
        try {
            const res = await axiosInstance.put(`${COMPANY_ENDPOINTS.JOBS}/${jobId}`, data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Job update failed' });
            return null;
        } finally {
            set({ loading: false });
        }
    },
    updateJobStatus: async (jobId, isActive) => {
        try {
            const res = await axiosInstance.patch(COMPANY_ENDPOINTS.JOB_STATUS(jobId), { isActive });

            // Update the job in the local state
            set((state) => ({
                jobs: state.jobs.map(job =>
                    job._id === jobId ? { ...job, isActive } : job
                )
            }));

            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Status update failed' });
            return null;
        }
    },
    getJobApplications: async (jobId) => {
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.JOB_APPLICATIONS(jobId));
            return res.data.applications;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch applications failed' });
            return [];
        }
    },
    // Dashboard
    getDashboardData: async () => {
        try {
            const res = await axiosInstance.get(COMPANY_ENDPOINTS.DASHBOARD);
            set({ dashboard: res.data });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Dashboard fetch failed' });
        }
    },
    // Questions
    createQuestion: async (data) => {
        try {
            const res = await axiosInstance.post(QUESTION_ENDPOINTS.CREATE, data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Create question failed' });
            return null;
        }
    },
    getQuestions: async () => {
        try {
            const res = await axiosInstance.get(QUESTION_ENDPOINTS.GET_ALL);
            set({ questions: res.data.questions });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch questions failed' });
        }
    },
    updateQuestion: async (id, data) => {
        try {
            const res = await axiosInstance.patch(QUESTION_ENDPOINTS.BY_ID(id), data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update question failed' });
            return null;
        }
    },
    deleteQuestion: async (id) => {
        try {
            await axiosInstance.delete(QUESTION_ENDPOINTS.BY_ID(id));
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Delete question failed' });
        }
    },
    // Tests
    createTest: async (data) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.CREATE, data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Create test failed' });
            return null;
        }
    },
    getTests: async () => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.GET_ALL);
            set({ tests: res.data.tests });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch tests failed' });
        }
    },
    getTestDetails: async (id) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.BY_ID(id));
            set({ testDetails: res.data.test });
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Test detail fetch failed' });
        }
    },
    assignTest: async (testId, assignData) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.ASSIGN(testId), assignData);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Assign test failed' });
            return null;
        }
    },
    getTestResults: async (testId) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.RESULTS(testId));
            return res.data.results;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch results failed' });
            return [];
        }
    },

    getTestAnalytics: async (testId) => {
        try {
            const res = await axiosInstance.get(TEST_ENDPOINTS.ANALYTICS(testId));
            return res.data.analytics;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Analytics fetch failed' });
            return null;
        }
    },

    submitFeedback: async (testId, participantId, feedback) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.PARTICIPANT_FEEDBACK(testId, participantId), feedback);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Feedback submit failed' });
            return null;
        }
    },

    updateTest: async (testId, data) => {
        try {
            const res = await axiosInstance.put(TEST_ENDPOINTS.UPDATE(testId), data);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update test failed' });
            return null;
        }
    },

    deleteTest: async (testId) => {
        try {
            await axiosInstance.delete(TEST_ENDPOINTS.DELETE(testId));
            // Remove from local state
            set((state) => ({
                tests: state.tests.filter(test => test._id !== testId)
            }));
            return { success: true };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Delete test failed' });
            return null;
        }
    },

    addQuestionsToTest: async (testId, questions) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.ADD_QUESTIONS(testId), { questions });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Add questions failed' });
            return null;
        }
    },

    removeQuestionsFromTest: async (testId, questionIds) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.REMOVE_QUESTIONS(testId), { questionIds });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Remove questions failed' });
            return null;
        }
    },

    assignCandidatesToTest: async (testId, candidateIds) => {
        try {
            const res = await axiosInstance.post(TEST_ENDPOINTS.ASSIGN_CANDIDATES(testId), { candidateIds });
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Assign candidates failed' });
            return null;
        }
    },

    // Candidates
    getCandidates: async () => {
        try {
            const res = await axiosInstance.get(CANDIDATE_ENDPOINTS.GET_ALL);
            set({ candidates: res.data.candidates || [] });
            return res.data.candidates;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch candidates failed' });
            return [];
        }
    },

    searchCandidates: async (searchTerm, filters = {}) => {
        try {
            const params = new URLSearchParams({ search: searchTerm, ...filters });
            const res = await axiosInstance.get(`${CANDIDATE_ENDPOINTS.SEARCH}?${params}`);
            return res.data.candidates || [];
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Search candidates failed' });
            return [];
        }
    },

    getAvailableCandidatesForTest: async (testId) => {
        try {
            const res = await axiosInstance.get(`${CANDIDATE_ENDPOINTS.AVAILABLE_FOR_TEST}?testId=${testId}`);
            return res.data.candidates || [];
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch available candidates failed' });
            return [];
        }
    },

    // Question Categories and Filtering
    getQuestionCategories: async () => {
        try {
            const res = await axiosInstance.get(QUESTION_ENDPOINTS.CATEGORIES);
            set({ questionCategories: res.data.categories || [] });
            return res.data.categories;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch categories failed' });
            return [];
        }
    },

    getQuestionsByCategory: async (category) => {
        try {
            const res = await axiosInstance.get(`${QUESTION_ENDPOINTS.BY_CATEGORY}?category=${category}`);
            return res.data.questions || [];
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch questions by category failed' });
            return [];
        }
    },

    filterQuestions: async (filters) => {
        try {
            const params = new URLSearchParams(filters);
            const res = await axiosInstance.get(`${QUESTION_ENDPOINTS.FILTER}?${params}`);
            return res.data.questions || [];
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Filter questions failed' });
            return [];
        }
    },

    // Question Bundles
    createQuestionBundle: async (bundleData) => {
        try {
            const res = await axiosInstance.post(QUESTION_BUNDLE_ENDPOINTS.CREATE, bundleData);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Create bundle failed' });
            return null;
        }
    },

    getQuestionBundles: async () => {
        try {
            const res = await axiosInstance.get(QUESTION_BUNDLE_ENDPOINTS.GET_ALL);
            set({ questionBundles: res.data.bundles || [] });
            return res.data.bundles;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Fetch bundles failed' });
            return [];
        }
    },

    updateQuestionBundle: async (bundleId, bundleData) => {
        try {
            const res = await axiosInstance.put(QUESTION_BUNDLE_ENDPOINTS.UPDATE(bundleId), bundleData);
            return res.data;
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Update bundle failed' });
            return null;
        }
    },

    deleteQuestionBundle: async (bundleId) => {
        try {
            await axiosInstance.delete(QUESTION_BUNDLE_ENDPOINTS.DELETE(bundleId));
            set((state) => ({
                questionBundles: state.questionBundles.filter(bundle => bundle._id !== bundleId)
            }));
            return { success: true };
        } catch (err) {
            set({ error: err?.response?.data?.error || 'Delete bundle failed' });
            return null;
        }
    },

    clearError: () => set({ error: null }),
}));

export default useCompanyStore;
