import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  CheckIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  DocumentTextIcon,
  PlusIcon,
  UserGroupIcon,
  BriefcaseIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

// Import new components
import QuestionSelector from './QuestionSelector';
import CandidateAssignment from './CandidateAssignment';

const TestCreationWizard = ({ 
  isOpen, 
  onClose, 
  onComplete,
  editingTest = null,
  jobs = [],
  questions = [],
  candidates = []
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [testData, setTestData] = useState({
    // Step 1: Basic Test Info
    testName: '',
    description: '',
    duration: 60,
    passingScore: 50,
    scheduledDate: '',
    endDate: '',
    instructions: '',
    allowedAttempts: 1,
    randomizeQuestions: false,
    showResults: false,
    
    // Step 2: Questions
    questions: [],
    
    // Step 3: Assignment
    associatedJobs: [],
    participants: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps = [
    { id: 1, title: 'Test Details', icon: DocumentTextIcon },
    { id: 2, title: 'Add Questions', icon: PlusIcon },
    { id: 3, title: 'Assign Test', icon: UserGroupIcon }
  ];

  // Initialize form data
  useEffect(() => {
    if (editingTest) {
      setTestData({
        testName: editingTest.testName || '',
        description: editingTest.description || '',
        duration: editingTest.duration || 60,
        passingScore: editingTest.passingScore || 50,
        scheduledDate: editingTest.scheduledDate ? new Date(editingTest.scheduledDate).toISOString().slice(0, 16) : '',
        endDate: editingTest.endDate ? new Date(editingTest.endDate).toISOString().slice(0, 16) : '',
        instructions: editingTest.instructions || '',
        allowedAttempts: editingTest.allowedAttempts || 1,
        randomizeQuestions: editingTest.randomizeQuestions || false,
        showResults: editingTest.showResults || false,
        questions: editingTest.questions || [],
        associatedJobs: editingTest.associatedJobs || [],
        participants: editingTest.participants || []
      });
    }
  }, [editingTest, isOpen]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep(1);
      setTestData({
        testName: '',
        description: '',
        duration: 60,
        passingScore: 50,
        scheduledDate: '',
        endDate: '',
        instructions: '',
        allowedAttempts: 1,
        randomizeQuestions: false,
        showResults: false,
        questions: [],
        associatedJobs: [],
        participants: []
      });
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const handleChange = (field, value) => {
    setTestData(prev => ({ ...prev, [field]: value }));
  };

  const validateStep = (step) => {
    switch (step) {
      case 1:
        return testData.testName.trim() && 
               testData.scheduledDate && 
               testData.endDate &&
               new Date(testData.scheduledDate) < new Date(testData.endDate);
      case 2:
        return testData.questions.length > 0;
      case 3:
        return testData.associatedJobs.length > 0 || testData.participants.length > 0;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    } else {
      toast.error('Please complete all required fields');
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleComplete = async () => {
    if (!validateStep(3)) {
      toast.error('Please assign the test to at least one job or candidate');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Prepare data for backend
      const finalTestData = {
        ...testData,
        scheduledDate: new Date(testData.scheduledDate).toISOString(),
        endDate: new Date(testData.endDate).toISOString(),
        totalPoints: testData.questions.reduce((sum, q) => sum + (q.points || 1), 0)
      };

      const result = await onComplete(finalTestData);
      
      if (result?.success !== false) {
        toast.success(editingTest ? 'Test updated successfully' : 'Test created successfully');
        onClose();
      } else {
        if (result?.details && Array.isArray(result.details)) {
          result.details.forEach(error => toast.error(error));
        } else {
          toast.error(result?.error || 'Failed to save test');
        }
      }
    } catch (error) {
      toast.error('An error occurred while saving the test');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => {
        const StepIcon = step.icon;
        const isActive = currentStep === step.id;
        const isCompleted = currentStep > step.id;
        
        return (
          <div key={step.id} className="flex items-center">
            <div className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
              isActive ? 'bg-blue-100 text-blue-700' :
              isCompleted ? 'bg-green-100 text-green-700' :
              'bg-gray-100 text-gray-500'
            }`}>
              <StepIcon className="h-5 w-5" />
              <span className="font-medium">{step.title}</span>
              {isCompleted && <CheckIcon className="h-4 w-4" />}
            </div>
            {index < steps.length - 1 && (
              <ArrowRightIcon className="h-4 w-4 text-gray-400 mx-2" />
            )}
          </div>
        );
      })}
    </div>
  );

  const renderTextField = (label, field, options = {}) => {
    const { placeholder, isTextarea = false, maxLength, rows = 3, required = false, type = 'text' } = options;
    const value = testData[field];

    return (
      <div className="space-y-2">
        <label className="block text-sm font-semibold text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
          {maxLength && (
            <span className="text-xs text-gray-500 ml-2">
              ({value?.length || 0}/{maxLength})
            </span>
          )}
        </label>
        {isTextarea ? (
          <textarea
            value={value}
            onChange={(e) => handleChange(field, e.target.value)}
            placeholder={placeholder}
            rows={rows}
            maxLength={maxLength}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
          />
        ) : (
          <input
            type={type}
            value={value}
            onChange={(e) => handleChange(field, type === 'number' ? parseInt(e.target.value) || 0 : e.target.value)}
            placeholder={placeholder}
            maxLength={maxLength}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
          />
        )}
      </div>
    );
  };

  const renderCheckbox = (label, field, description = '') => (
    <div className="flex items-start gap-3">
      <input
        type="checkbox"
        checked={testData[field]}
        onChange={(e) => handleChange(field, e.target.checked)}
        className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
      />
      <div>
        <label className="text-sm font-semibold text-gray-700">{label}</label>
        {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
      </div>
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">Test Details</h3>
      
      {/* Basic Information */}
      <div className="space-y-4">
        {renderTextField('Test Name', 'testName', { 
          placeholder: 'Enter test name...', 
          required: true 
        })}
        
        {renderTextField('Description', 'description', { 
          placeholder: 'Enter test description...', 
          isTextarea: true, 
          maxLength: 500,
          rows: 3
        })}
      </div>

      {/* Test Configuration */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {renderTextField('Duration (minutes)', 'duration', { 
          type: 'number', 
          placeholder: '60',
          required: true 
        })}
        
        {renderTextField('Passing Score (%)', 'passingScore', { 
          type: 'number', 
          placeholder: '50',
          required: true 
        })}
        
        {renderTextField('Allowed Attempts', 'allowedAttempts', { 
          type: 'number', 
          placeholder: '1',
          required: true 
        })}
      </div>

      {/* Schedule */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {renderTextField('Scheduled Date', 'scheduledDate', { 
          type: 'datetime-local', 
          required: true 
        })}
        
        {renderTextField('End Date', 'endDate', { 
          type: 'datetime-local', 
          required: true 
        })}
      </div>

      {/* Instructions */}
      {renderTextField('Instructions', 'instructions', { 
        placeholder: 'Enter test instructions for candidates...', 
        isTextarea: true, 
        maxLength: 1000,
        rows: 4
      })}

      {/* Test Options */}
      <div className="space-y-3">
        <h4 className="font-semibold text-gray-800">Test Options</h4>
        
        {renderCheckbox(
          'Randomize Questions',
          'randomizeQuestions',
          'Questions will be presented in random order for each candidate'
        )}
        
        {renderCheckbox(
          'Show Results',
          'showResults',
          'Candidates will see their results immediately after completing the test'
        )}
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">Add Questions</h3>
        <div className="text-sm text-gray-600">
          {testData.questions.length} questions selected
        </div>
      </div>

      {/* Question Selection Methods */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={() => {/* Open question selection modal */}}
          className="p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all text-center"
        >
          <PlusIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <div className="font-medium text-gray-700">Select Individual Questions</div>
          <div className="text-sm text-gray-500 mt-1">Choose specific questions</div>
        </button>

        <button
          onClick={() => {/* Open category filter */}}
          className="p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all text-center"
        >
          <DocumentTextIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <div className="font-medium text-gray-700">Filter by Category</div>
          <div className="text-sm text-gray-500 mt-1">Add questions by category</div>
        </button>

        <button
          onClick={() => {/* Open bundle selection */}}
          className="p-6 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all text-center"
        >
          <UserGroupIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <div className="font-medium text-gray-700">Question Bundles</div>
          <div className="text-sm text-gray-500 mt-1">Pre-made question sets</div>
        </button>
      </div>

      {/* Selected Questions */}
      {testData.questions.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-800">Selected Questions</h4>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {testData.questions.map((question, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-800 truncate">
                    {question.questionText || `Question ${index + 1}`}
                  </p>
                  <p className="text-xs text-gray-500">
                    {question.questionType || 'MCQ'} • {question.category || 'General'}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    value={question.points || 1}
                    onChange={(e) => {
                      const newQuestions = [...testData.questions];
                      newQuestions[index].points = parseInt(e.target.value) || 1;
                      handleChange('questions', newQuestions);
                    }}
                    min="1"
                    max="10"
                    className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                  />
                  <span className="text-xs text-gray-500">pts</span>
                  <button
                    type="button"
                    onClick={() => {
                      const newQuestions = testData.questions.filter((_, i) => i !== index);
                      handleChange('questions', newQuestions);
                    }}
                    className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Total Points:</strong> {testData.questions.reduce((sum, q) => sum + (q.points || 1), 0)}
            </p>
          </div>
        </div>
      )}

      {testData.questions.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <DocumentTextIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>No questions selected yet</p>
          <p className="text-sm">Choose questions using the options above</p>
        </div>
      )}
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">Assign Test</h3>

      {/* Assignment Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Assign to Jobs */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <BriefcaseIcon className="h-5 w-5 text-blue-600" />
            <h4 className="font-semibold text-gray-800">Assign to Jobs</h4>
          </div>

          <div className="space-y-2 max-h-48 overflow-y-auto">
            {jobs.map((job) => (
              <div key={job._id} className="flex items-center gap-3 p-3 border rounded-lg">
                <input
                  type="checkbox"
                  checked={testData.associatedJobs.includes(job._id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      handleChange('associatedJobs', [...testData.associatedJobs, job._id]);
                    } else {
                      handleChange('associatedJobs', testData.associatedJobs.filter(id => id !== job._id));
                    }
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{job.title}</div>
                  <div className="text-sm text-gray-500">{job.department} • {job.location}</div>
                  <div className="text-xs text-gray-400">{job.applicants?.length || 0} applicants</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Assign to Candidates */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <UserGroupIcon className="h-5 w-5 text-green-600" />
            <h4 className="font-semibold text-gray-800">Assign to Candidates</h4>
          </div>

          <div className="space-y-2 max-h-48 overflow-y-auto">
            {candidates.map((candidate) => (
              <div key={candidate._id} className="flex items-center gap-3 p-3 border rounded-lg">
                <input
                  type="checkbox"
                  checked={testData.participants.some(p => p.candidateId === candidate._id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      handleChange('participants', [
                        ...testData.participants,
                        { candidateId: candidate._id, status: 'assigned' }
                      ]);
                    } else {
                      handleChange('participants', testData.participants.filter(p => p.candidateId !== candidate._id));
                    }
                  }}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{candidate.name}</div>
                  <div className="text-sm text-gray-500">{candidate.email}</div>
                  {candidate.skills && (
                    <div className="text-xs text-gray-400">
                      {candidate.skills.slice(0, 3).join(', ')}
                      {candidate.skills.length > 3 && ` +${candidate.skills.length - 3} more`}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Assignment Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h5 className="font-semibold text-gray-800 mb-2">Assignment Summary</h5>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Jobs:</span>
            <span className="ml-2 font-medium">{testData.associatedJobs.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Direct Candidates:</span>
            <span className="ml-2 font-medium">{testData.participants.length}</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
              onClick={onClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl"
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {editingTest ? 'Edit Test' : 'Create New Test'}
                  </h2>
                  <div className="flex items-center mt-2">
                    {[1, 2, 3].map((step) => (
                      <div key={step} className="flex items-center">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                            currentStep >= step
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-200 text-gray-600'
                          }`}
                        >
                          {currentStep > step ? (
                            <CheckIcon className="h-4 w-4" />
                          ) : (
                            step
                          )}
                        </div>
                        {step < 3 && (
                          <div
                            className={`w-12 h-1 mx-2 ${
                              currentStep > step ? 'bg-blue-600' : 'bg-gray-200'
                            }`}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              {/* Content */}
              <div className="mb-8">
                {currentStep === 1 && renderStep1()}
                {currentStep === 2 && renderStep2()}
                {currentStep === 3 && renderStep3()}
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between pt-6 border-t">
                <button
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                  className="flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ArrowLeftIcon className="h-4 w-4" />
                  Previous
                </button>

                <div className="flex items-center gap-3">
                  {currentStep < 3 ? (
                    <button
                      onClick={handleNext}
                      className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Next
                      <ArrowRightIcon className="h-4 w-4" />
                    </button>
                  ) : (
                    <button
                      onClick={handleComplete}
                      disabled={isSubmitting}
                      className="flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          {editingTest ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        <>
                          <CheckIcon className="h-4 w-4" />
                          {editingTest ? 'Update Test' : 'Create Test'}
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default TestCreationWizard;
