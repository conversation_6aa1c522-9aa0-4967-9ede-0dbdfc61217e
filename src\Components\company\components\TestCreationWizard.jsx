import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  XMarkIcon, 
  CheckIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  DocumentTextIcon,
  PlusIcon,
  UserGroupIcon,
  BriefcaseIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const TestCreationWizard = ({ 
  isOpen, 
  onClose, 
  onComplete,
  editingTest = null,
  jobs = [],
  questions = [],
  candidates = []
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [testData, setTestData] = useState({
    // Step 1: Basic Test Info
    testName: '',
    description: '',
    duration: 60,
    passingScore: 50,
    scheduledDate: '',
    endDate: '',
    instructions: '',
    allowedAttempts: 1,
    randomizeQuestions: false,
    showResults: false,
    
    // Step 2: Questions
    questions: [],
    
    // Step 3: Assignment
    associatedJobs: [],
    participants: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps = [
    { id: 1, title: 'Test Details', icon: DocumentTextIcon },
    { id: 2, title: 'Add Questions', icon: PlusIcon },
    { id: 3, title: 'Assign Test', icon: UserGroupIcon }
  ];

  // Initialize form data
  useEffect(() => {
    if (editingTest) {
      setTestData({
        testName: editingTest.testName || '',
        description: editingTest.description || '',
        duration: editingTest.duration || 60,
        passingScore: editingTest.passingScore || 50,
        scheduledDate: editingTest.scheduledDate ? new Date(editingTest.scheduledDate).toISOString().slice(0, 16) : '',
        endDate: editingTest.endDate ? new Date(editingTest.endDate).toISOString().slice(0, 16) : '',
        instructions: editingTest.instructions || '',
        allowedAttempts: editingTest.allowedAttempts || 1,
        randomizeQuestions: editingTest.randomizeQuestions || false,
        showResults: editingTest.showResults || false,
        questions: editingTest.questions || [],
        associatedJobs: editingTest.associatedJobs || [],
        participants: editingTest.participants || []
      });
    }
  }, [editingTest, isOpen]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep(1);
      setTestData({
        testName: '',
        description: '',
        duration: 60,
        passingScore: 50,
        scheduledDate: '',
        endDate: '',
        instructions: '',
        allowedAttempts: 1,
        randomizeQuestions: false,
        showResults: false,
        questions: [],
        associatedJobs: [],
        participants: []
      });
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const handleChange = (field, value) => {
    setTestData(prev => ({ ...prev, [field]: value }));
  };

  const validateStep = (step) => {
    switch (step) {
      case 1:
        return testData.testName.trim() && 
               testData.scheduledDate && 
               testData.endDate &&
               new Date(testData.scheduledDate) < new Date(testData.endDate);
      case 2:
        return testData.questions.length > 0;
      case 3:
        return testData.associatedJobs.length > 0 || testData.participants.length > 0;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    } else {
      toast.error('Please complete all required fields');
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleComplete = async () => {
    if (!validateStep(3)) {
      toast.error('Please assign the test to at least one job or candidate');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Prepare data for backend
      const finalTestData = {
        ...testData,
        scheduledDate: new Date(testData.scheduledDate).toISOString(),
        endDate: new Date(testData.endDate).toISOString(),
        totalPoints: testData.questions.reduce((sum, q) => sum + (q.points || 1), 0)
      };

      const result = await onComplete(finalTestData);
      
      if (result?.success !== false) {
        toast.success(editingTest ? 'Test updated successfully' : 'Test created successfully');
        onClose();
      } else {
        if (result?.details && Array.isArray(result.details)) {
          result.details.forEach(error => toast.error(error));
        } else {
          toast.error(result?.error || 'Failed to save test');
        }
      }
    } catch (error) {
      toast.error('An error occurred while saving the test');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => {
        const StepIcon = step.icon;
        const isActive = currentStep === step.id;
        const isCompleted = currentStep > step.id;
        
        return (
          <div key={step.id} className="flex items-center">
            <div className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
              isActive ? 'bg-blue-100 text-blue-700' :
              isCompleted ? 'bg-green-100 text-green-700' :
              'bg-gray-100 text-gray-500'
            }`}>
              <StepIcon className="h-5 w-5" />
              <span className="font-medium">{step.title}</span>
              {isCompleted && <CheckIcon className="h-4 w-4" />}
            </div>
            {index < steps.length - 1 && (
              <ArrowRightIcon className="h-4 w-4 text-gray-400 mx-2" />
            )}
          </div>
        );
      })}
    </div>
  );

  const renderTextField = (label, field, options = {}) => {
    const { placeholder, isTextarea = false, maxLength, rows = 3, required = false, type = 'text' } = options;
    const value = testData[field];

    return (
      <div className="space-y-2">
        <label className="block text-sm font-semibold text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
          {maxLength && (
            <span className="text-xs text-gray-500 ml-2">
              ({value?.length || 0}/{maxLength})
            </span>
          )}
        </label>
        {isTextarea ? (
          <textarea
            value={value}
            onChange={(e) => handleChange(field, e.target.value)}
            placeholder={placeholder}
            rows={rows}
            maxLength={maxLength}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
          />
        ) : (
          <input
            type={type}
            value={value}
            onChange={(e) => handleChange(field, type === 'number' ? parseInt(e.target.value) || 0 : e.target.value)}
            placeholder={placeholder}
            maxLength={maxLength}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
          />
        )}
      </div>
    );
  };

  const renderCheckbox = (label, field, description = '') => (
    <div className="flex items-start gap-3">
      <input
        type="checkbox"
        checked={testData[field]}
        onChange={(e) => handleChange(field, e.target.checked)}
        className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
      />
      <div>
        <label className="text-sm font-semibold text-gray-700">{label}</label>
        {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
      </div>
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">Test Details</h3>
      
      {/* Basic Information */}
      <div className="space-y-4">
        {renderTextField('Test Name', 'testName', { 
          placeholder: 'Enter test name...', 
          required: true 
        })}
        
        {renderTextField('Description', 'description', { 
          placeholder: 'Enter test description...', 
          isTextarea: true, 
          maxLength: 500,
          rows: 3
        })}
      </div>

      {/* Test Configuration */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {renderTextField('Duration (minutes)', 'duration', { 
          type: 'number', 
          placeholder: '60',
          required: true 
        })}
        
        {renderTextField('Passing Score (%)', 'passingScore', { 
          type: 'number', 
          placeholder: '50',
          required: true 
        })}
        
        {renderTextField('Allowed Attempts', 'allowedAttempts', { 
          type: 'number', 
          placeholder: '1',
          required: true 
        })}
      </div>

      {/* Schedule */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {renderTextField('Scheduled Date', 'scheduledDate', { 
          type: 'datetime-local', 
          required: true 
        })}
        
        {renderTextField('End Date', 'endDate', { 
          type: 'datetime-local', 
          required: true 
        })}
      </div>

      {/* Instructions */}
      {renderTextField('Instructions', 'instructions', { 
        placeholder: 'Enter test instructions for candidates...', 
        isTextarea: true, 
        maxLength: 1000,
        rows: 4
      })}

      {/* Test Options */}
      <div className="space-y-3">
        <h4 className="font-semibold text-gray-800">Test Options</h4>

        {renderCheckbox(
          'Randomize Questions',
          'randomizeQuestions',
          'Questions will be presented in random order for each candidate'
        )}

        {renderCheckbox(
          'Show Results',
          'showResults',
          'Candidates will see their results immediately after completing the test'
        )}
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">Add Questions</h3>

      {/* Question Selection Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <button
          onClick={() => setShowQuestionModal(true)}
          className="p-6 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-all text-center"
        >
          <DocumentTextIcon className="h-12 w-12 text-blue-600 mx-auto mb-3" />
          <h4 className="font-semibold text-gray-800 mb-2">Browse Questions</h4>
          <p className="text-sm text-gray-600">Filter by category, difficulty, or use saved bundles</p>
        </button>

        <div className="p-6 border border-gray-200 rounded-lg bg-gray-50">
          <h4 className="font-semibold text-gray-800 mb-2">Quick Stats</h4>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex justify-between">
              <span>Available Questions:</span>
              <span className="font-medium">{questions.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Selected:</span>
              <span className="font-medium text-blue-600">{testData.questions.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Total Points:</span>
              <span className="font-medium text-green-600">
                {testData.questions.reduce((sum, q) => sum + (q.points || 1), 0)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Selected Questions Display */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-semibold text-gray-800">Selected Questions ({testData.questions.length})</h4>
          {testData.questions.length > 0 && (
            <button
              onClick={() => handleChange('questions', [])}
              className="text-sm text-red-600 hover:text-red-800 font-medium"
            >
              Clear All
            </button>
          )}
        </div>

        {questions.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-600 mb-4">No questions available</p>
            <p className="text-sm text-gray-500">Create questions first in the Question Management section</p>
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
            {questions.map((question) => {
              const isSelected = testData.questions.some(q => q.questionId === question._id);
              const selectedQuestion = testData.questions.find(q => q.questionId === question._id);

              return (
                <div key={question._id} className="p-4 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-start gap-3">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => {
                        if (e.target.checked) {
                          handleChange('questions', [
                            ...testData.questions,
                            { questionId: question._id, points: 1, optionsSnapshot: [] }
                          ]);
                        } else {
                          handleChange('questions',
                            testData.questions.filter(q => q.questionId !== question._id)
                          );
                        }
                      }}
                      className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />

                    <div className="flex-1">
                      <div className="font-medium text-gray-800 mb-1">
                        {question.questionText}
                      </div>
                      <div className="text-sm text-gray-600 mb-2">
                        Type: {question.type} | Difficulty: {question.difficulty}
                      </div>

                      {isSelected && (
                        <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Points for this question:
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="10"
                            value={selectedQuestion?.points || 1}
                            onChange={(e) => {
                              const points = parseInt(e.target.value) || 1;
                              handleChange('questions',
                                testData.questions.map(q =>
                                  q.questionId === question._id
                                    ? { ...q, points }
                                    : q
                                )
                              );
                            }}
                            className="w-20 px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {testData.questions.length > 0 && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium text-blue-800">
                Total Questions: {testData.questions.length}
              </span>
              <span className="font-medium text-blue-800">
                Total Points: {testData.questions.reduce((sum, q) => sum + (q.points || 1), 0)}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">Assign Test</h3>

      {/* Job Assignment */}
      <div className="space-y-4">
        <h4 className="font-semibold text-gray-800 flex items-center gap-2">
          <BriefcaseIcon className="h-5 w-5" />
          Associate with Jobs
        </h4>

        {jobs.length === 0 ? (
          <div className="text-center py-6 bg-gray-50 rounded-lg">
            <BriefcaseIcon className="h-10 w-10 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600">No jobs available</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {jobs.map((job) => (
              <div key={job._id} className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
                <input
                  type="checkbox"
                  checked={testData.associatedJobs.includes(job._id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      handleChange('associatedJobs', [...testData.associatedJobs, job._id]);
                    } else {
                      handleChange('associatedJobs',
                        testData.associatedJobs.filter(id => id !== job._id)
                      );
                    }
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{job.title}</div>
                  <div className="text-sm text-gray-600">{job.department}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Summary */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-semibold text-gray-800 mb-3">Assignment Summary</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Associated Jobs:</span>
            <span className="font-medium">{testData.associatedJobs.length}</span>
          </div>
          <div className="flex justify-between">
            <span>Direct Participants:</span>
            <span className="font-medium">{testData.participants.length}</span>
          </div>
        </div>

        {testData.associatedJobs.length === 0 && testData.participants.length === 0 && (
          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded text-yellow-800 text-sm">
            Please assign the test to at least one job or candidate to continue.
          </div>
        )}
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      default:
        return renderStep1();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">
                {editingTest ? 'Edit Test' : 'Create New Test'}
              </h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Step Indicator */}
            {renderStepIndicator()}
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {renderCurrentStep()}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div className="flex items-center gap-2">
              {currentStep > 1 && (
                <button
                  onClick={handlePrevious}
                  className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <ArrowLeftIcon className="h-4 w-4" />
                  Previous
                </button>
              )}
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>

              {currentStep < 3 ? (
                <button
                  onClick={handleNext}
                  disabled={!validateStep(currentStep)}
                  className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium"
                >
                  Next
                  <ArrowRightIcon className="h-4 w-4" />
                </button>
              ) : (
                <button
                  onClick={handleComplete}
                  disabled={isSubmitting || !validateStep(3)}
                  className="flex items-center gap-2 px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      {editingTest ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4" />
                      {editingTest ? 'Update Test' : 'Create Test'}
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default TestCreationWizard;
