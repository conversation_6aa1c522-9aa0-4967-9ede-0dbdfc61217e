import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  XMarkIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  CheckIcon,
  BookmarkIcon,
  DocumentTextIcon,
  TagIcon,
  AcademicCapIcon,
  ClockIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import useCompanyStore from '../../../store/companyStore';
import toast from 'react-hot-toast';

const QuestionBundleModal = ({ 
  isOpen, 
  onClose, 
  onAddToTest,
  existingQuestions = []
}) => {
  const { 
    questions,
    questionCategories,
    questionBundles,
    getQuestions,
    getQuestionCategories,
    getQuestionBundles,
    filterQuestions,
    createQuestionBundle,
    loading 
  } = useCompanyStore();

  const [activeTab, setActiveTab] = useState('filter'); // 'filter', 'bundles'
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  const [filteredQuestions, setFilteredQuestions] = useState([]);
  const [filters, setFilters] = useState({
    category: '',
    difficulty: '',
    type: '',
    searchTerm: ''
  });
  const [showCreateBundle, setShowCreateBundle] = useState(false);
  const [bundleName, setBundleName] = useState('');
  const [bundleDescription, setBundleDescription] = useState('');

  useEffect(() => {
    if (isOpen) {
      initializeData();
    }
  }, [isOpen]);

  useEffect(() => {
    handleFilter();
  }, [filters, questions]);

  const initializeData = async () => {
    await Promise.all([
      getQuestions(),
      getQuestionCategories(),
      getQuestionBundles()
    ]);
  };

  const handleFilter = async () => {
    if (Object.values(filters).some(f => f)) {
      const filtered = await filterQuestions(filters);
      setFilteredQuestions(filtered);
    } else {
      setFilteredQuestions(questions);
    }
  };

  const handleQuestionSelect = (question) => {
    setSelectedQuestions(prev => {
      const isSelected = prev.some(q => q._id === question._id);
      if (isSelected) {
        return prev.filter(q => q._id !== question._id);
      } else {
        return [...prev, { ...question, points: 1 }];
      }
    });
  };

  const handleSelectAll = () => {
    const availableQuestions = filteredQuestions.filter(
      q => !existingQuestions.some(eq => eq.questionId === q._id)
    );
    
    if (selectedQuestions.length === availableQuestions.length) {
      setSelectedQuestions([]);
    } else {
      setSelectedQuestions(availableQuestions.map(q => ({ ...q, points: 1 })));
    }
  };

  const updateQuestionPoints = (questionId, points) => {
    setSelectedQuestions(prev =>
      prev.map(q => q._id === questionId ? { ...q, points } : q)
    );
  };

  const handleCreateBundle = async () => {
    if (!bundleName.trim()) {
      toast.error('Please enter a bundle name');
      return;
    }
    
    if (selectedQuestions.length === 0) {
      toast.error('Please select at least one question');
      return;
    }

    const bundleData = {
      name: bundleName,
      description: bundleDescription,
      questions: selectedQuestions.map(q => ({
        questionId: q._id,
        points: q.points || 1
      })),
      totalQuestions: selectedQuestions.length,
      totalPoints: selectedQuestions.reduce((sum, q) => sum + (q.points || 1), 0)
    };

    const result = await createQuestionBundle(bundleData);
    if (result) {
      toast.success('Question bundle created successfully');
      setBundleName('');
      setBundleDescription('');
      setSelectedQuestions([]);
      setShowCreateBundle(false);
      await getQuestionBundles(); // Refresh bundles
    }
  };

  const handleAddBundleToTest = (bundle) => {
    const questionsToAdd = bundle.questions.map(q => ({
      questionId: q.questionId,
      points: q.points || 1,
      optionsSnapshot: []
    }));
    
    onAddToTest(questionsToAdd);
    toast.success(`Added ${bundle.questions.length} questions from "${bundle.name}" to test`);
  };

  const handleAddSelectedToTest = () => {
    if (selectedQuestions.length === 0) {
      toast.error('Please select at least one question');
      return;
    }

    const questionsToAdd = selectedQuestions.map(q => ({
      questionId: q._id,
      points: q.points || 1,
      optionsSnapshot: []
    }));
    
    onAddToTest(questionsToAdd);
    toast.success(`Added ${selectedQuestions.length} questions to test`);
    setSelectedQuestions([]);
  };

  const isQuestionSelected = (question) => {
    return selectedQuestions.some(q => q._id === question._id);
  };

  const isQuestionInTest = (question) => {
    return existingQuestions.some(eq => eq.questionId === question._id);
  };

  const availableQuestions = filteredQuestions.filter(q => !isQuestionInTest(q));

  const renderFilterTab = () => (
    <div className="space-y-6">
      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <input
          type="text"
          placeholder="Search questions..."
          value={filters.searchTerm}
          onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />

        <select
          value={filters.category}
          onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Categories</option>
          {questionCategories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>

        <select
          value={filters.difficulty}
          onChange={(e) => setFilters(prev => ({ ...prev, difficulty: e.target.value }))}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Difficulties</option>
          <option value="Easy">Easy</option>
          <option value="Medium">Medium</option>
          <option value="Hard">Hard</option>
        </select>

        <select
          value={filters.type}
          onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Types</option>
          <option value="MCQ">Multiple Choice</option>
          <option value="Coding">Coding</option>
          <option value="Short Answer">Short Answer</option>
        </select>
      </div>

      {/* Selection Summary */}
      <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
        <div className="flex items-center gap-4">
          <span className="text-sm text-gray-600">
            {availableQuestions.length} questions available
          </span>
          <span className="text-sm font-medium text-blue-600">
            {selectedQuestions.length} selected
          </span>
          <span className="text-sm text-gray-600">
            Total points: {selectedQuestions.reduce((sum, q) => sum + (q.points || 1), 0)}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={handleSelectAll}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            {selectedQuestions.length === availableQuestions.length ? 'Deselect All' : 'Select All'}
          </button>
          
          {selectedQuestions.length > 0 && (
            <button
              onClick={() => setShowCreateBundle(true)}
              className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
            >
              <BookmarkIcon className="h-4 w-4" />
              Save as Bundle
            </button>
          )}
        </div>
      </div>

      {/* Questions List */}
      <div className="max-h-96 overflow-y-auto space-y-3">
        {availableQuestions.map((question, index) => {
          const isSelected = isQuestionSelected(question);
          const selectedQuestion = selectedQuestions.find(q => q._id === question._id);
          
          return (
            <motion.div
              key={question._id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.02 }}
              className={`p-4 border-2 rounded-lg transition-all ${
                isSelected
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300'
              }`}
            >
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => handleQuestionSelect(question)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                
                <div className="flex-1">
                  <div className="font-medium text-gray-800 mb-2">
                    {question.questionText}
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                    <div className="flex items-center gap-1">
                      <TagIcon className="h-4 w-4" />
                      {question.category || 'General'}
                    </div>
                    <div className="flex items-center gap-1">
                      <AcademicCapIcon className="h-4 w-4" />
                      {question.difficulty || 'Medium'}
                    </div>
                    <div className="flex items-center gap-1">
                      <DocumentTextIcon className="h-4 w-4" />
                      {question.type || 'MCQ'}
                    </div>
                  </div>
                  
                  {isSelected && (
                    <div className="mt-3 flex items-center gap-2">
                      <label className="text-sm font-medium text-gray-700">Points:</label>
                      <input
                        type="number"
                        min="1"
                        max="10"
                        value={selectedQuestion?.points || 1}
                        onChange={(e) => updateQuestionPoints(question._id, parseInt(e.target.value) || 1)}
                        className="w-16 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );

  const renderBundlesTab = () => (
    <div className="space-y-4">
      {questionBundles.length === 0 ? (
        <div className="text-center py-12">
          <BookmarkIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">No Question Bundles</h3>
          <p className="text-gray-500">Create your first question bundle to reuse across tests</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {questionBundles.map((bundle, index) => (
            <motion.div
              key={bundle._id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-gray-800">{bundle.name}</h4>
                <button
                  onClick={() => handleAddBundleToTest(bundle)}
                  className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <PlusIcon className="h-4 w-4" />
                  Add to Test
                </button>
              </div>
              
              {bundle.description && (
                <p className="text-gray-600 text-sm mb-3">{bundle.description}</p>
              )}
              
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span>{bundle.totalQuestions || bundle.questions?.length || 0} questions</span>
                <span>{bundle.totalPoints || 0} points</span>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">Question Management</h2>
                <p className="text-blue-100">Filter questions or use saved bundles</p>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex gap-4 mt-6">
              <button
                onClick={() => setActiveTab('filter')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'filter'
                    ? 'bg-white text-blue-700'
                    : 'text-blue-100 hover:text-white hover:bg-white/20'
                }`}
              >
                Filter Questions
              </button>
              <button
                onClick={() => setActiveTab('bundles')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === 'bundles'
                    ? 'bg-white text-blue-700'
                    : 'text-blue-100 hover:text-white hover:bg-white/20'
                }`}
              >
                Question Bundles
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
                <span className="ml-3 text-gray-600">Loading...</span>
              </div>
            ) : (
              activeTab === 'filter' ? renderFilterTab() : renderBundlesTab()
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-600">
              {activeTab === 'filter' && `${selectedQuestions.length} question(s) selected`}
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              
              {activeTab === 'filter' && selectedQuestions.length > 0 && (
                <button
                  onClick={handleAddSelectedToTest}
                  className="flex items-center gap-2 px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                >
                  <PlusIcon className="h-4 w-4" />
                  Add {selectedQuestions.length} Question(s) to Test
                </button>
              )}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Create Bundle Modal */}
      {showCreateBundle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white rounded-xl shadow-xl w-full max-w-md p-6"
          >
            <h3 className="text-lg font-bold text-gray-800 mb-4">Create Question Bundle</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Bundle Name</label>
                <input
                  type="text"
                  value={bundleName}
                  onChange={(e) => setBundleName(e.target.value)}
                  placeholder="e.g., JavaScript Fundamentals"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
                <textarea
                  value={bundleDescription}
                  onChange={(e) => setBundleDescription(e.target.value)}
                  placeholder="Brief description of this question bundle"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                />
              </div>
              
              <div className="text-sm text-gray-600">
                {selectedQuestions.length} questions, {selectedQuestions.reduce((sum, q) => sum + (q.points || 1), 0)} total points
              </div>
            </div>
            
            <div className="flex items-center gap-3 mt-6">
              <button
                onClick={() => setShowCreateBundle(false)}
                className="flex-1 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateBundle}
                className="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors font-medium"
              >
                Create Bundle
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default QuestionBundleModal;
