// PUT /api/tests/:testId - Update Test
// Enhanced with validation and status management

exports.updateTest = async (req, res) => {
  try {
    const { testId } = req.params;
    const companyId = req.user.companyId;
    const updates = req.body;

    // Find the test
    const test = await Test.findOne({ _id: testId, companyId });
    if (!test) {
      return res.status(404).json({
        success: false,
        error: 'Test not found'
      });
    }

    // Check if test can be modified based on status
    const canModify = ['draft', 'scheduled'].includes(test.status);
    if (!canModify) {
      return res.status(400).json({
        success: false,
        error: `Cannot modify test in ${test.status} status`,
        allowedStatuses: ['draft', 'scheduled']
      });
    }

    // Validate update fields
    const allowedUpdates = [
      'title', 'description', 'duration', 'totalMarks', 'passingMarks',
      'instructions', 'startDate', 'endDate', 'timeLimit', 'shuffleQuestions',
      'showResults', 'allowReview', 'proctoring', 'status'
    ];

    const updateFields = {};
    Object.keys(updates).forEach(key => {
      if (allowedUpdates.includes(key)) {
        updateFields[key] = updates[key];
      }
    });

    // Enhanced validations
    if (updateFields.startDate && updateFields.endDate) {
      if (new Date(updateFields.startDate) >= new Date(updateFields.endDate)) {
        return res.status(400).json({
          success: false,
          error: 'Start date must be before end date'
        });
      }
    }

    if (updateFields.passingMarks && updateFields.totalMarks) {
      if (updateFields.passingMarks > updateFields.totalMarks) {
        return res.status(400).json({
          success: false,
          error: 'Passing marks cannot exceed total marks'
        });
      }
    }

    // Update test
    const updatedTest = await Test.findByIdAndUpdate(
      testId,
      { 
        ...updateFields,
        updatedAt: new Date(),
        updatedBy: req.user._id
      },
      { new: true, runValidators: true }
    ).populate([
      { path: 'questions.questionId', select: 'questionText type category difficulty' },
      { path: 'createdBy', select: 'name email' },
      { path: 'participants.candidateId', select: 'name email' }
    ]);

    // Log the update activity
    await TestActivity.create({
      testId,
      userId: req.user._id,
      action: 'test_updated',
      details: `Test updated: ${Object.keys(updateFields).join(', ')}`,
      timestamp: new Date()
    });

    res.json({
      success: true,
      message: 'Test updated successfully',
      test: updatedTest,
      updatedFields: Object.keys(updateFields)
    });

  } catch (error) {
    console.error('Update test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Enhanced validation middleware for test updates
exports.validateTestUpdate = (req, res, next) => {
  const { body } = req;
  
  // Validate duration
  if (body.duration && (body.duration < 1 || body.duration > 480)) {
    return res.status(400).json({
      success: false,
      error: 'Duration must be between 1 and 480 minutes'
    });
  }

  // Validate marks
  if (body.totalMarks && body.totalMarks < 1) {
    return res.status(400).json({
      success: false,
      error: 'Total marks must be at least 1'
    });
  }

  // Validate status transitions
  if (body.status) {
    const allowedStatuses = ['draft', 'scheduled', 'active', 'completed', 'cancelled'];
    if (!allowedStatuses.includes(body.status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status',
        allowedStatuses
      });
    }
  }

  next();
};
